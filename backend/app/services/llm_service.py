"""
LLM调用服务
"""
import json
import httpx
from pathlib import Path
from typing import AsyncGenerator

from app.services.config_service import get_llm_config
from app.services.report_service import get_report_content


def load_prompt_template(template_name: str) -> str:
    """从文件加载提示词模板"""
    try:
        # 获取prompts目录路径
        current_dir = Path(__file__).parent.parent
        prompts_dir = current_dir / "prompts"
        template_file = prompts_dir / f"{template_name}.txt"
        
        if not template_file.exists():
            print(f"[WARNING] Prompt template file not found: {template_file}")
            return ""
            
        with open(template_file, "r", encoding="utf-8") as f:
            content = f.read()
            
        print(f"[DEBUG] Loaded prompt template: {template_name}, length: {len(content)}")
        return content
        
    except Exception as e:
        print(f"[ERROR] Failed to load prompt template {template_name}: {e}")
        return ""


async def generate_llm_response(
    task_id: str,
    user_message: str,
    context_html: str = ""
) -> AsyncGenerator[str, None]:
    """调用LLM生成流式响应"""

    print(f"[DEBUG] LLM Service: Starting response generation")
    print(f"[DEBUG] LLM Service: User message length: {(user_message)}")
    print(f"[DEBUG] LLM Service: Context HTML length: {(context_html)}")

    # 获取LLM配置
    llm_config = await get_llm_config()
    print(f"[DEBUG] LLM Service: Got LLM config - URL: {llm_config['llm_base_url']}, Model: {llm_config['llm_model']}")
    
    # 构建消息
    messages = []
    
    report = await get_report_content(task_id)
    print(f"[DEBUG] LLM Service: Got report content - length: {len(report.content) if report else 0}")

    # 根据是否有HTML内容选择不同的处理模式
    if report and report.content and  report.content.strip():
        # 修改模式：已有HTML内容，需要生成修改指令
        print(f"[DEBUG] LLM Service: Using modify mode")
        system_prompt = load_prompt_template("modify_report")
        if not system_prompt:
            # 如果加载失败，使用默认的修改提示词
            system_prompt = """你是一个HTML报告修改专家。根据用户需求，生成精确的HTML代码修改指令。
请按照JSON格式输出修改指令，包含action和modifications字段。"""

        messages.append( {"role": "system", "content": system_prompt})
        messages.append({
            "role": "user", 
            "content": f"## 已有的代码内容: {report.content} \n\n ##当前用户选中的内容：\n\n{context_html}\n\n##用户请求：{user_message}"
        })
    else:
        # 生成模式：没有HTML内容，需要生成完整报告
        print(f"[DEBUG] LLM Service: Using generate mode")
        system_prompt = load_prompt_template("generate_report")
        if not system_prompt:
            # 如果加载失败，使用默认的生成提示词
            system_prompt = """你是一个报告撰写智能体。你擅长用html语言，按照用户的要求，撰写美观的报告。
请用html语言生成报告，使用<code_snippet></code_snippet>包裹生成的html代码。"""
        messages.append( {"role": "system", "content": system_prompt})
        messages.append({
            "role": "user", 
            "content": f"## 用户请求：{user_message}"
        })
    
    print(f"[DEBUG] LLM Service: Messages built - length: {messages}")
    # 准备请求数据
    request_data = {
        "model": llm_config["llm_model"],
        "messages": messages,
        "stream": True,
        "temperature": 0.7,
        "max_tokens": 40000
    }

    headers = {
        "Authorization": f"Bearer {llm_config['llm_api_key']}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"[DEBUG] LLM Service: Making HTTP request to {llm_config['llm_base_url']}")
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream(
                "POST",
                llm_config["llm_base_url"],
                json=request_data,
                headers=headers
            ) as response:

                print(f"[DEBUG] LLM Service: HTTP response status: {response.status_code}")
                if response.status_code != 200:
                    error_text = await response.aread()
                    error_msg = f"错误：LLM API调用失败 ({response.status_code}): {error_text.decode()}"
                    print(f"[DEBUG] LLM Service: Error - {error_msg}")
                    yield error_msg
                    return

                print(f"[DEBUG] LLM Service: Starting to process streaming response")
                chunk_count = 0
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀

                        if data_str.strip() == "[DONE]":
                            print(f"[DEBUG] LLM Service: Received [DONE] signal, total chunks: {chunk_count}")
                            break

                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    content = delta["content"]
                                    if content:
                                        chunk_count += 1
                                        if chunk_count <= 5:  # 只打印前5个chunk的详细信息
                                            print(f"[DEBUG] LLM Service: Yielding chunk {chunk_count}: {repr(content[:50])}")
                                        elif chunk_count % 50 == 0:  # 每50个chunk打印一次进度
                                            print(f"[DEBUG] LLM Service: Progress - chunk {chunk_count}")
                                        yield content
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"[DEBUG] LLM Service: Error processing chunk: {e}")
                            continue
    
    except httpx.TimeoutException:
        yield "错误：LLM API调用超时"
    except httpx.RequestError as e:
        yield f"错误：网络请求失败: {str(e)}"
    except Exception as e:
        yield f"错误：LLM调用异常: {str(e)}"
