"""
WebSocket流式对话处理
"""
import json
import re
from typing import <PERSON><PERSON>
from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from app.services.task_service import get_or_create_task, save_report_file
from app.services.message_service import save_message
from app.services.llm_service import generate_llm_response
from app.services.report_service import update_report_content

websocket_router = APIRouter()

CODE_SNIPPET_HEAD = "<code_snippet>"
CODE_SNIPPET_TAIL = "</code_snippet>"


def check_if_possible_endwith_reserved_key(text: str, reserved_key: str) -> Tuple[bool, int]:
    """检查文本是否可能以保留关键字结尾（部分匹配）"""
    for i in range(1, len(reserved_key) + 1):  # 从1开始，避免空字符串匹配
        prefix = reserved_key[:i]
        if text.endswith(prefix):
            return True, len(text) - len(prefix)

    return False, -1

def check_if_possible_startwith_reserved_key(text: str, reserved_key: str) -> <PERSON><PERSON>[bool, int]:
    """检查文本是否可能以保留关键字开始（部分匹配）"""
    for i in range(len(reserved_key)):
        suffix = reserved_key[i:]
        if text.startswith(suffix):
            return True, len(suffix)

    return False, -1

@websocket_router.websocket("/ws/chat/{task_id}")
async def websocket_chat(websocket: WebSocket, task_id: str):
    """WebSocket聊天端点"""
    await websocket.accept()
    
    try:
        # 确保任务存在
        task = await get_or_create_task(task_id)
        
        while True:
            # 接收用户消息
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "content": "Invalid JSON format"
                })
                continue
            
            user_message = message_data.get("content", "")
            context_html = message_data.get("context_html", "")
            
            if not user_message.strip():
                continue
            
            # 保存用户消息
            await save_message(task_id, "user", user_message)
            
            # 生成LLM响应
            full_response = ""
            text_response = ""  # 只包含文本部分的响应
            in_code_snippet = False
            current_code_buffer = ""  # 初始化代码缓冲区
            has_code_snippet = False  # 标记是否包含代码片段

            try:
                print(f"[DEBUG] Starting LLM response generation for task {task_id}")
                chunk_count = 0
                async for chunk in generate_llm_response(user_message, context_html):
                    chunk_count += 1
                    full_response += chunk
                    print(f"[DEBUG] Received chunk {chunk_count}, length: {len(chunk)}, in_code_snippet: {in_code_snippet}")
                    print(f"[DEBUG] Chunk content preview: {repr(chunk[:100])}")

                    # 当前生成内容不是代码内容
                    if not in_code_snippet:
                        # 检查是否包含完整的代码片段开始标签
                        if CODE_SNIPPET_HEAD in full_response:
                            # 找到完整的代码片段开始标签
                            start_idx = full_response.find(CODE_SNIPPET_HEAD)

                            # 发现代码片段开始
                            has_code_snippet = True
                            in_code_snippet = True

                            # 发送代码片段前的文本部分
                            text_before_code = full_response[:start_idx]
                            if len(text_before_code) > len(text_response):
                                chunk_text = text_before_code[len(text_response):]
                                if chunk_text:
                                    await websocket.send_json({
                                        "type": "assistant_message_chunk",
                                        "content": chunk_text,
                                        "is_code_snippet": False
                                    })
                                    text_response += chunk_text

                            # 初始化代码缓冲区
                            current_code_buffer = full_response[start_idx:]

                            # 发送代码片段开始信号
                            await websocket.send_json({
                                "type": "code_snippet_start",
                                "content": "",
                                "is_code_snippet": True
                            })
                            code_start_sent = True

                        else:
                            # 检查是否可能开始代码片段（部分匹配）
                            flag, start_buffer_idx = check_if_possible_endwith_reserved_key(full_response, CODE_SNIPPET_HEAD)
                            if not flag:
                                # 普通文本，直接发送
                                await websocket.send_json({
                                    "type": "assistant_message_chunk",
                                    "content": chunk,
                                    "is_code_snippet": False
                                })
                                text_response += chunk
                            else:
                                # 可能开始代码片段，但还不完整，暂时不发送这部分内容
                                text_before_possible_code = full_response[:start_buffer_idx]
                                if len(text_before_possible_code) > len(text_response):
                                    chunk_text = text_before_possible_code[len(text_response):]
                                    if chunk_text:
                                        await websocket.send_json({
                                            "type": "assistant_message_chunk",
                                            "content": chunk_text,
                                            "is_code_snippet": False
                                        })
                                        text_response += chunk_text

                    # 当前生成内容是代码内容
                    else:
                        current_code_buffer += chunk

                        # 检查是否包含完整的代码片段尾部
                        if CODE_SNIPPET_TAIL in current_code_buffer:
                            end_idx = current_code_buffer.find(CODE_SNIPPET_TAIL)

                            # 提取代码片段后的文本
                            text_after_code = current_code_buffer[end_idx + len(CODE_SNIPPET_TAIL):]
                            if text_after_code:
                                text_response += text_after_code

                            # 保留完整的代码片段（包括标签）
                            complete_code_snippet = current_code_buffer[:end_idx + len(CODE_SNIPPET_TAIL)]

                            # 提取纯HTML内容（去除标签）
                            html_content = complete_code_snippet.removeprefix(CODE_SNIPPET_HEAD).removesuffix(CODE_SNIPPET_TAIL)

                            # 保存报告文件
                            try:
                                report_path = await save_report_file(task_id, html_content)
                                print(f"[DEBUG] Report saved to: {report_path}")

                                # 发送代码片段结束信号，包含报告路径
                                await websocket.send_json({
                                    "type": "code_snippet_end",
                                    "content": "",
                                    "report_file_path": report_path
                                })
                            except Exception as e:
                                print(f"[DEBUG] Error saving report: {str(e)}")
                                await websocket.send_json({
                                    "type": "error",
                                    "content": f"Failed to save report: {str(e)}"
                                })

                            # 如果有代码片段后的文本，发送它
                            if text_after_code:
                                await websocket.send_json({
                                    "type": "assistant_message_chunk",
                                    "content": text_after_code,
                                    "is_code_snippet": False
                                })

                            in_code_snippet = False
                            current_code_buffer = complete_code_snippet  # 保存完整的代码片段用于后续处理

                print(f"[DEBUG] LLM response generation completed. Total chunks: {chunk_count}")
                print(f"[DEBUG] Text response length: {len(text_response)}, Full response length: {len(full_response)}")
                
            except Exception as e:
                await websocket.send_json({
                    "type": "error",
                    "content": f"LLM调用失败: {str(e)}"
                })

            # 保存助手的文本响应（无论是否有异常都要保存）
            try:
                message_content = text_response.strip() if text_response.strip() else "生成了报告内容"
                print(f"Saving assistant message for task {task_id}: {message_content[:50]}... )")

                await save_message(
                    task_id,
                    "assistant",
                    message_content,
                    has_code_snippet
                )

                # 如果有代码片段，同时更新报告内容到数据库
                if has_code_snippet and current_code_buffer:
                    html_content = current_code_buffer.removeprefix(CODE_SNIPPET_HEAD).removesuffix(CODE_SNIPPET_TAIL)
                    await update_report_content(task_id, html_content)

                print(f"Assistant message saved successfully for task {task_id}")
            except Exception as e:
                print(f"Failed to save assistant message for task {task_id}: {e}")
    
    except WebSocketDisconnect:
        print(f"WebSocket disconnected for task {task_id}")
    except Exception as e:
        print(f"WebSocket error for task {task_id}: {str(e)}")
        try:
            await websocket.send_json({
                "type": "error",
                "content": f"服务器错误: {str(e)}"
            })
        except:
            pass
