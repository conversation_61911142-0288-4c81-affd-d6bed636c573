#!/usr/bin/env python3
"""
测试新增的LLM服务函数
"""

import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.llm_service import intent_analyze, replace_code_snippet, generate_code_snippet


async def test_intent_analyze():
    """测试意图分析函数"""
    print("=" * 50)
    print("测试意图分析函数")
    print("=" * 50)
    
    test_cases = [
        "请把第二章的标题改成'市场分析报告'",
        "生成一个关于气候变化的完整报告",
        "在销售数据部分添加一个柱状图",
        "删除第三个表格",
        "修改图表的颜色为蓝色"
    ]
    
    for i, user_input in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {user_input}")
        try:
            result = await intent_analyze(user_input)
            print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"错误: {e}")


async def test_replace_code_snippet():
    """测试代码片段替换函数"""
    print("\n" + "=" * 50)
    print("测试代码片段替换函数")
    print("=" * 50)
    
    user_request = "请把这个标题改成'2024年销售业绩报告'"
    intent_result = {
        "operation_type": "replace",
        "target_element": "title",
        "content_type": "text",
        "description": "将标题替换为'2024年销售业绩报告'",
        "keywords": ["标题", "2024年销售业绩报告"],
        "confidence": 0.95
    }
    current_html = "<h1 class='section-h1'>原始标题</h1>"
    
    print(f"用户请求: {user_request}")
    print(f"当前HTML: {current_html}")
    
    try:
        result = await replace_code_snippet(
            user_request=user_request,
            intent_result=intent_result,
            current_html_snippet=current_html,
            context_info="这是一个报告的主标题"
        )
        print(f"替换结果:\n{result}")
    except Exception as e:
        print(f"错误: {e}")


async def test_generate_code_snippet():
    """测试完整报告生成函数"""
    print("\n" + "=" * 50)
    print("测试完整报告生成函数")
    print("=" * 50)
    
    user_request = "生成一个关于2024年第一季度销售数据的分析报告"
    intent_result = {
        "operation_type": "generate",
        "target_element": "entire",
        "content_type": "text",
        "description": "生成2024年第一季度销售数据分析报告",
        "keywords": ["2024年", "第一季度", "销售数据", "分析报告"],
        "confidence": 0.92
    }
    
    print(f"用户请求: {user_request}")
    
    try:
        result = await generate_code_snippet(
            user_request=user_request,
            intent_result=intent_result,
            context_info="需要包含销售数据图表和分析结论"
        )
        print(f"生成结果长度: {len(result)} 字符")
        print(f"生成结果预览:\n{result[:500]}...")
        
        # 保存到文件以便查看
        with open("generated_report_test.html", "w", encoding="utf-8") as f:
            f.write(result)
        print("完整结果已保存到 generated_report_test.html")
        
    except Exception as e:
        print(f"错误: {e}")


async def main():
    """主测试函数"""
    print("开始测试LLM服务新增函数...")
    
    # 测试意图分析
    await test_intent_analyze()
    
    # 测试代码片段替换
    await test_replace_code_snippet()
    
    # 测试完整报告生成
    await test_generate_code_snippet()
    
    print("\n" + "=" * 50)
    print("所有测试完成")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
