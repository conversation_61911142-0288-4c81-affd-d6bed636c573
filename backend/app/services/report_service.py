"""
报告文件管理服务
"""
import os
from datetime import datetime
from pathlib import Path
from typing import Optional

from app.core.config import get_reports_dir
from app.services.task_service import get_task_by_id, update_task_report_path
from app.models.schemas import ReportContentResponse


async def get_report_content(task_id: str) -> Optional[ReportContentResponse]:
    """获取报告内容"""
    try:
        # 获取任务信息
        print(f"[DEBUG] ReportService: Getting report content for task {task_id}")
        task = await get_task_by_id(task_id)
        if not task:
            return None
            
        # 构建报告文件路径
        if task.report_file_path:
            # 使用任务中存储的路径
            report_file = Path(task.report_file_path) / "report.html"
        else:
            # 使用默认路径
            reports_dir = get_reports_dir()
            report_file = reports_dir / task_id / "report.html"
            
        # 检查文件是否存在
        if not report_file.exists():
            return None
        print(f"[DEBUG] ReportService: Report file exists: {report_file}")
        # 读取文件内容
        with open(report_file, "r", encoding="utf-8") as f:
            content = f.read()
            
        # 获取文件修改时间
        stat = report_file.stat()
        last_modified = datetime.fromtimestamp(stat.st_mtime)
        
        return ReportContentResponse(
            content=content,
            file_path=str(report_file),
            last_modified=last_modified
        )
        
    except Exception as e:
        print(f"[ERROR] ReportService: Failed to get report content for task {task_id}: {e}")
        return None


async def update_report_content(task_id: str, content: str) -> Optional[ReportContentResponse]:
    """更新报告内容"""
    try:
        # 获取任务信息
        task = await get_task_by_id(task_id)
        if not task:
            return None
            
        # 确定报告文件路径
        if task.report_file_path:
            task_dir = Path(task.report_file_path)
        else:
            reports_dir = get_reports_dir()
            task_dir = reports_dir / task_id
            
        # 确保目录存在
        task_dir.mkdir(parents=True, exist_ok=True)
        
        # 报告文件路径
        report_file = task_dir / "report.html"
        
        # 写入文件内容
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(content)
            
        # 更新任务的报告路径（如果之前没有设置）
        if not task.report_file_path:
            await update_task_report_path(task_id, str(task_dir))
            
        # 获取文件修改时间
        stat = report_file.stat()
        last_modified = datetime.fromtimestamp(stat.st_mtime)
        
        print(f"[DEBUG] ReportService: Successfully updated report for task {task_id}")
        
        return ReportContentResponse(
            content=content,
            file_path=str(report_file),
            last_modified=last_modified
        )
        
    except Exception as e:
        print(f"[ERROR] ReportService: Failed to update report content for task {task_id}: {e}")
        return None


async def backup_report_content(task_id: str) -> Optional[str]:
    """备份报告内容"""
    try:
        # 获取当前报告内容
        report_response = await get_report_content(task_id)
        if not report_response:
            return None
            
        # 创建备份文件名（带时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"report_backup_{timestamp}.html"
        
        # 确定备份路径
        task = await get_task_by_id(task_id)
        if task and task.report_file_path:
            backup_dir = Path(task.report_file_path) / "backups"
        else:
            reports_dir = get_reports_dir()
            backup_dir = reports_dir / task_id / "backups"
            
        # 确保备份目录存在
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份文件路径
        backup_file = backup_dir / backup_filename
        
        # 写入备份文件
        with open(backup_file, "w", encoding="utf-8") as f:
            f.write(report_response.content)
            
        print(f"[DEBUG] ReportService: Created backup for task {task_id}: {backup_file}")
        
        return str(backup_file)
        
    except Exception as e:
        print(f"[ERROR] ReportService: Failed to backup report for task {task_id}: {e}")
        return None


async def list_report_backups(task_id: str) -> list[str]:
    """列出报告备份文件"""
    try:
        # 确定备份路径
        task = await get_task_by_id(task_id)
        if task and task.report_file_path:
            backup_dir = Path(task.report_file_path) / "backups"
        else:
            reports_dir = get_reports_dir()
            backup_dir = reports_dir / task_id / "backups"
            
        # 检查备份目录是否存在
        if not backup_dir.exists():
            return []
            
        # 获取所有备份文件
        backup_files = []
        for file in backup_dir.glob("report_backup_*.html"):
            backup_files.append(str(file))
            
        # 按修改时间排序（最新的在前）
        backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        
        return backup_files
        
    except Exception as e:
        print(f"[ERROR] ReportService: Failed to list backups for task {task_id}: {e}")
        return []
