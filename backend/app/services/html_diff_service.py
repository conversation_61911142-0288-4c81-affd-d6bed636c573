"""
HTML差异应用服务
用于将大模型生成的修改指令应用到现有HTML代码中
"""
import json
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup, Tag, NavigableString


class HTMLDiffApplier:
    """HTML差异应用器"""
    
    def __init__(self, html_content: str):
        """初始化HTML差异应用器
        
        Args:
            html_content: 原始HTML内容
        """
        self.original_html = html_content
        self.soup = BeautifulSoup(html_content, 'html.parser')
    
    def apply_modifications(self, modifications_json: str) -> str:
        """应用修改指令
        
        Args:
            modifications_json: JSON格式的修改指令
            
        Returns:
            修改后的HTML内容
        """
        try:
            modifications_data = json.loads(modifications_json)
            
            if not isinstance(modifications_data, dict) or "modifications" not in modifications_data:
                print(f"[WARNING] Invalid modifications format: {modifications_json[:100]}")
                return self.original_html
            
            modifications = modifications_data["modifications"]
            
            for modification in modifications:
                self._apply_single_modification(modification)
            
            return str(self.soup)
            
        except json.JSONDecodeError as e:
            print(f"[ERROR] Failed to parse modifications JSON: {e}")
            return self.original_html
        except Exception as e:
            print(f"[ERROR] Failed to apply modifications: {e}")
            return self.original_html
    
    def _apply_single_modification(self, modification: Dict[str, Any]) -> None:
        """应用单个修改指令
        
        Args:
            modification: 单个修改指令
        """
        mod_type = modification.get("type")
        target = modification.get("target")
        content = modification.get("content", "")
        description = modification.get("description", "")
        
        print(f"[DEBUG] Applying modification: {mod_type} on {target} - {description}")
        
        try:
            if mod_type == "replace":
                self._apply_replace(target, content)
            elif mod_type == "insert":
                position = modification.get("position", "after")
                self._apply_insert(target, content, position)
            elif mod_type == "delete":
                self._apply_delete(target)
            elif mod_type == "update":
                attribute = modification.get("attribute")
                self._apply_update(target, content, attribute)
            else:
                print(f"[WARNING] Unknown modification type: {mod_type}")
                
        except Exception as e:
            print(f"[ERROR] Failed to apply modification {mod_type}: {e}")
    
    def _find_elements(self, selector: str) -> List[Tag]:
        """根据选择器查找元素
        
        Args:
            selector: CSS选择器或描述性文本
            
        Returns:
            匹配的元素列表
        """
        try:
            # 尝试作为CSS选择器
            elements = self.soup.select(selector)
            if elements:
                return elements
        except Exception:
            pass
        
        # 尝试作为ID选择器
        if selector.startswith('#'):
            element = self.soup.find(id=selector[1:])
            return [element] if element else []
        
        # 尝试作为类选择器
        if selector.startswith('.'):
            elements = self.soup.find_all(class_=selector[1:])
            return elements
        
        # 尝试作为标签名
        elements = self.soup.find_all(selector)
        if elements:
            return elements
        
        # 尝试通过文本内容查找
        elements = self.soup.find_all(string=re.compile(selector, re.IGNORECASE))
        parent_elements = []
        for element in elements:
            if element.parent and element.parent not in parent_elements:
                parent_elements.append(element.parent)
        
        return parent_elements
    
    def _apply_replace(self, target: str, content: str) -> None:
        """应用替换操作"""
        elements = self._find_elements(target)
        
        if not elements:
            print(f"[WARNING] No elements found for target: {target}")
            return
        
        for element in elements:
            # 解析新内容
            new_soup = BeautifulSoup(content, 'html.parser')
            new_element = new_soup.find()
            
            if new_element:
                element.replace_with(new_element)
            else:
                # 如果是纯文本，直接替换
                element.string = content
    
    def _apply_insert(self, target: str, content: str, position: str) -> None:
        """应用插入操作"""
        elements = self._find_elements(target)
        
        if not elements:
            print(f"[WARNING] No elements found for target: {target}")
            return
        
        # 解析新内容
        new_soup = BeautifulSoup(content, 'html.parser')
        new_elements = new_soup.find_all()
        
        for element in elements:
            for new_element in new_elements:
                if position == "after":
                    element.insert_after(new_element)
                elif position == "before":
                    element.insert_before(new_element)
                elif position == "append":
                    element.append(new_element)
                elif position == "prepend":
                    element.insert(0, new_element)
    
    def _apply_delete(self, target: str) -> None:
        """应用删除操作"""
        elements = self._find_elements(target)
        
        for element in elements:
            element.decompose()
    
    def _apply_update(self, target: str, content: str, attribute: Optional[str] = None) -> None:
        """应用更新操作"""
        elements = self._find_elements(target)
        
        if not elements:
            print(f"[WARNING] No elements found for target: {target}")
            return
        
        for element in elements:
            if attribute == "textContent" or attribute is None:
                # 更新文本内容
                element.string = content
            elif attribute:
                # 更新属性
                element[attribute] = content


def apply_html_diff(original_html: str, modifications_json: str) -> str:
    """应用HTML差异修改
    
    Args:
        original_html: 原始HTML内容
        modifications_json: JSON格式的修改指令
        
    Returns:
        修改后的HTML内容
    """
    applier = HTMLDiffApplier(original_html)
    return applier.apply_modifications(modifications_json)


def extract_modifications_from_response(llm_response: str) -> Optional[str]:
    """从LLM响应中提取修改指令
    
    Args:
        llm_response: LLM的完整响应
        
    Returns:
        提取的JSON修改指令，如果没有找到则返回None
    """
    try:
        # 尝试查找JSON代码块
        json_pattern = r'```json\s*(\{.*?\})\s*```'
        match = re.search(json_pattern, llm_response, re.DOTALL)
        
        if match:
            return match.group(1)
        
        # 尝试查找<code_snippet>标签中的JSON
        code_pattern = r'<code_snippet>\s*(\{.*?\})\s*</code_snippet>'
        match = re.search(code_pattern, llm_response, re.DOTALL)
        
        if match:
            return match.group(1)
        
        # 尝试直接查找JSON对象
        json_pattern = r'\{[^{}]*"action"[^{}]*"modifications"[^{}]*\}'
        match = re.search(json_pattern, llm_response, re.DOTALL)
        
        if match:
            return match.group(0)
        
        return None
        
    except Exception as e:
        print(f"[ERROR] Failed to extract modifications from response: {e}")
        return None
